import React from 'react';
import { <PERSON>, CardHeader, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Progress } from '../ui/Progress';
import { useOptimizationStore } from '../../store/optimizationStore';
import { formatDate } from '../../lib/utils';

export function Dashboard({ onNavigate }) {
  const { optimizationHistory, stats } = useOptimizationStore();
  
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-display-md text-gray-900">Dashboard</h1>
          <p className="text-body-lg text-gray-600 mt-1">Welcome back, let's optimize some prompts!</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="text-center py-6">
                <div className="text-display-sm font-bold text-primary-600">{stats.totalOptimizations}</div>
                <div className="text-body-md text-gray-600 mt-1">Total Optimizations</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="text-center py-6">
                <div className="text-display-sm font-bold text-success-600">{stats.avgSuccessRate}%</div>
                <div className="text-body-md text-gray-600 mt-1">Avg Success Rate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="text-center py-6">
                <div className="text-display-sm font-bold text-accent-600">{stats.avgImprovement}x</div>
                <div className="text-body-md text-gray-600 mt-1">Avg Improvement</div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Optimizations */}
          <Card>
            <CardHeader>
              <h2 className="text-heading-lg">Recent Optimizations</h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {optimizationHistory.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-semibold text-body-lg text-gray-900">{item.title}</h3>
                        {item.status === 'complete' ? (
                          <Badge variant="success">✅ {item.successRate}% Success</Badge>
                        ) : item.status === 'processing' ? (
                          <Badge variant="warning">🔄 Processing</Badge>
                        ) : (
                          <Badge>{item.status}</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-sm text-gray-500">
                      {formatDate(item.timestamp)}
                    </div>
                  </div>
                ))}
                
                {optimizationHistory.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No optimizations yet. Start your first optimization!
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <h2 className="text-heading-lg">Quick Actions</h2>
            </CardHeader>
            <CardContent className="flex flex-col gap-3">
              <Button onClick={() => onNavigate('optimizer')} size="lg">New Optimization</Button>
              <Button variant="secondary">View Templates</Button>
              <Button variant="secondary">Import Prompts</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
