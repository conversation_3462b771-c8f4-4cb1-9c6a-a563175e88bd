import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input, Textarea } from '../ui/Input';
import { Progress } from '../ui/Progress';
import { Spinner } from '../ui/Spinner';
import { Badge } from '../ui/Badge';
import { useOptimizationStore } from '../../store/optimizationStore';

export function PromptOptimizer({ onNavigate }) {
  const [promptText, setPromptText] = useState('');
  const [promptTitle, setPromptTitle] = useState('');
  const [config, setConfig] = useState({
    technique: 'critique_n_refine',
    iterations: 3,
    batchSize: 5,
    successThreshold: 80
  });
  
  const { 
    startOptimization, 
    updateProgress,
    completeOptimization,
    isOptimizing, 
    currentOptimization 
  } = useOptimizationStore();
  
  const handleStartOptimization = async () => {
    if (!promptText || !promptTitle) return;
    
    // Start the optimization
    startOptimization({
      title: promptTitle,
      originalPrompt: promptText,
      config
    });
    
    // Simulate optimization process
    const stages = [
      { stage: 'initializing', progress: 10, duration: 1000 },
      { stage: 'generating', progress: 25, duration: 2000 },
      { stage: 'testing', progress: 45, duration: 2500 },
      { stage: 'critiquing', progress: 60, duration: 2000 },
      { stage: 'refining', progress: 80, duration: 2000 },
      { stage: 'evaluating', progress: 95, duration: 1500 },
      { stage: 'complete', progress: 100, duration: 500 }
    ];
    
    // Simulate progress through stages
    for (const { stage, progress, duration } of stages) {
      await new Promise(resolve => setTimeout(resolve, duration));
      updateProgress(progress, stage);
    }
    
    // Complete optimization with mock results
    setTimeout(() => {
      completeOptimization({
        optimizedPrompt: `[OPTIMIZED] ${promptText} - Enhanced with better context, clearer instructions, and improved structure for maximum LLM performance.`,
        successRate: 85 + Math.floor(Math.random() * 10),
        improvement: 1.5 + Math.random()
      });
      // Navigate back to dashboard
      onNavigate('dashboard');
    }, 500);
  };

  const stages = [
    { key: 'initializing', label: 'Initializing', icon: '⚙️' },
    { key: 'generating', label: 'Generating Variations', icon: '🔄' },
    { key: 'testing', label: 'Testing Performance', icon: '🧪' },
    { key: 'critiquing', label: 'Analyzing Results', icon: '🔍' },
    { key: 'refining', label: 'Refining Prompts', icon: '✨' },
    { key: 'evaluating', label: 'Final Evaluation', icon: '📊' },
    { key: 'complete', label: 'Complete', icon: '✅' }
  ];

  if (isOptimizing && currentOptimization) {
    // Show optimization progress
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <button 
            onClick={() => onNavigate('dashboard')}
            className="text-gray-600 hover:text-gray-900"
          >
            ← Back
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Optimization in Progress</h1>
        </div>

        <Card>
          <CardContent className="py-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">{currentOptimization.title}</h3>
                <p className="text-gray-600">{currentOptimization.originalPrompt}</p>
              </div>
              
              <Progress value={currentOptimization.progress || 0} showLabel />
              
              <div className="space-y-3">
                {stages.map((stageItem, index) => {
                  const isActive = currentOptimization.stage === stageItem.key;
                  const isPast = stages.findIndex(s => s.key === currentOptimization.stage) > index;
                  
                  return (
                    <div
                      key={stageItem.key}
                      className={`flex items-center space-x-3 p-3 rounded-lg ${
                        isActive ? 'bg-blue-50 border border-blue-200' : 
                        isPast ? 'opacity-50' : ''
                      }`}
                    >
                      <span className="text-lg">{stageItem.icon}</span>
                      <span className="text-base">{stageItem.label}</span>
                      {isActive && <Spinner className="ml-auto" size="sm" />}
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <button 
          onClick={() => onNavigate('dashboard')}
          className="text-gray-600 hover:text-gray-900"
        >
          ← Back to Dashboard
        </button>
        <h1 className="text-3xl font-bold text-gray-900">New Optimization</h1>
      </div>

      {/* Prompt Configuration */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">📝 Prompt Configuration</h2>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optimization Title
              </label>
              <Input
                placeholder="e.g., Marketing Email Generator"
                value={promptTitle}
                onChange={(e) => setPromptTitle(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Original Prompt
              </label>
              <Textarea
                placeholder="Enter your prompt to optimize..."
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                className="min-h-[150px]"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimization Settings */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">⚙️ Optimization Settings</h2>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Technique
              </label>
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={config.technique}
                onChange={(e) => setConfig({...config, technique: e.target.value})}
              >
                <option value="critique_n_refine">Critique & Refine</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Iterations: {config.iterations}
              </label>
              <input
                type="range"
                min="1"
                max="5"
                value={config.iterations}
                onChange={(e) => setConfig({...config, iterations: parseInt(e.target.value)})}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Batch Size: {config.batchSize} questions
              </label>
              <input
                type="range"
                min="5"
                max="20"
                step="5"
                value={config.batchSize}
                onChange={(e) => setConfig({...config, batchSize: parseInt(e.target.value)})}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Success Threshold: {config.successThreshold}%
              </label>
              <input
                type="range"
                min="70"
                max="95"
                step="5"
                value={config.successThreshold}
                onChange={(e) => setConfig({...config, successThreshold: parseInt(e.target.value)})}
                className="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button variant="secondary">Save Draft</Button>
        <Button 
          onClick={handleStartOptimization}
          disabled={!promptText || !promptTitle || isOptimizing}
          size="lg"
        >
          Start Optimization
        </Button>
      </div>
    </div>
  );
}